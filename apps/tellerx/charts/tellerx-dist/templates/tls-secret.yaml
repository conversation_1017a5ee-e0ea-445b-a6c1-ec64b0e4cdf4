{{- if .Values.ingress.enabled -}}
{{- if .Values.ingress.tls -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "tellerx-dist.fullname" . }}-tls-secret
  namespace: {{ .Values.global.namespace | default "default" }}
  labels:
    {{- include "tellerx-dist.labels" . | nindent 4 }}
type: kubernetes.io/tls
data:
  tls.crt: {{ .Files.Get "ssl/tls.crt" | b64enc }}
  tls.key: {{ .Files.Get "ssl/tls.key" | b64enc }}
{{- end }}
{{- end }}
