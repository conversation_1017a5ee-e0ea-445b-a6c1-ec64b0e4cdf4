# Override the full name of the chart
global:
  namespace: "onebank"

fullnameOverride: "tellerx-dist"

# Ingress configuration
ingress:
  enabled: true
  className: "alb"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    # 使用自签名证书，不需要 ACM ARN
  hosts:
    - host: "*"  # 请替换为您的实际域名
      paths:
        - path: /
          pathType: Prefix
          servicePort: 3000
  tls:
    - secretName: tellerx-dist-tls-secret  # 这将匹配模板中创建的 Secret
      hosts:
        - "*"  # 自签名证书支持所有域名
# Service configuration
service:
  port: 3000
  type: "ClusterIP"

# Replica count
replicas: 1

# Image configuration
image:
  repository: "************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/tellerx-dist"
  tag: "0.0.1-alpha"
  pullPolicy: "IfNotPresent"

# Resource limits and requests
resources:
  limits:
    cpu: "200m"
    memory: "256Mi"
  requests:
    cpu: "100m"
    memory: "128Mi"

# ConfigMap for nginx configuration
nginxConfig:
  enabled: true
  configPath: "/etc/nginx/"
  # Main nginx configuration content will be mounted from configmap

# Liveness and readiness probes
livenessProbe:
  httpGet:
    path: "/"
    port: 3000
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: "/"
    port: 3000
  initialDelaySeconds: 10
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3

# Pod security context is not needed, use default user